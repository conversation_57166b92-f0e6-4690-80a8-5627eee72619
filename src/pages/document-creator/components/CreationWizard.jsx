import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import StepProgressIndicator from './StepProgressIndicator';
import Input from '../../../components/ui/Input';

const CreationWizard = ({ currentStep, onStepChange, documentData, onDocumentDataChange }) => {
  const [formData, setFormData] = useState({
    documentType: documentData.documentType || 'ebook',
    niche: documentData.niche || '',
    targetAudience: documentData.targetAudience || '',
    keywords: documentData.keywords || '',
    tone: documentData.tone || 'academic',
    language: documentData.language || 'english',
    chapters: documentData.chapters || 5,
    subNiche: documentData.subNiche || '',
    template: documentData.template || 'modern'
  });

  // Updated steps to match Designrr flow
  const steps = [
    { id: 1, title: 'Generate', icon: 'Sparkles', description: 'Set up your document' },
    { id: 2, title: 'Edit content', icon: 'Edit3', description: 'Customize your content' },
    { id: 3, title: 'Choose template', icon: 'Layout', description: 'Select design' },
    { id: 4, title: 'Review', icon: 'Eye', description: 'Final review' },
    { id: 5, title: 'Publish', icon: 'Send', description: 'Export & share' }
  ];

  const documentTypes = [
    { id: 'ebook', name: 'eBook', icon: 'Book', description: 'Digital book with chapters and sections' },
    { id: 'academic', name: 'Academic Paper', icon: 'GraduationCap', description: 'Research papers, essays, theses' },
    { id: 'business', name: 'Business Document', icon: 'Briefcase', description: 'Reports, proposals, presentations' }
  ];

  const toneOptions = [
    { id: 'academic', name: 'Academic', description: 'Formal, scholarly tone' },
    { id: 'conversational', name: 'Conversational', description: 'Friendly, approachable tone' },
    { id: 'professional', name: 'Professional', description: 'Business-appropriate tone' }
  ];

  const languageOptions = [
    { id: 'english', name: 'English', flag: '🇺🇸' },
    { id: 'yoruba', name: 'Yoruba', flag: '🇳🇬' },
    { id: 'french', name: 'French', flag: '🇫🇷' }
  ];

  const handleInputChange = (field, value) => {
    const updatedData = { ...formData, [field]: value };
    setFormData(updatedData);
    onDocumentDataChange(updatedData);
  };

  const handleNext = () => {
    if (currentStep < steps.length) {
      onStepChange(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      onStepChange(currentStep - 1);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="text-center space-y-8">
            {/* Header */}
            <div>
              <h2 className="text-3xl font-bold text-text-primary mb-6">Project creator</h2>
              <p className="text-text-secondary text-xl leading-relaxed">
                I'd like to create an{' '}
                <select
                  value={formData.documentType}
                  onChange={(e) => handleInputChange('documentType', e.target.value)}
                  className="inline-block mx-1 px-3 py-1 border-b-2 border-primary bg-transparent text-primary font-semibold focus:outline-none hover:border-primary/80 transition-colors rounded-none"
                >
                  {documentTypes.map((type) => (
                    <option key={type.id} value={type.id}>
                      {type.name.toLowerCase()}
                    </option>
                  ))}
                </select>
                {' '}to be published as{' '}
                <select
                  value={formData.language}
                  onChange={(e) => handleInputChange('language', e.target.value)}
                  className="inline-block mx-1 px-3 py-1 border-b-2 border-primary bg-transparent text-primary font-semibold focus:outline-none hover:border-primary/80 transition-colors rounded-none"
                >
                  <option value="english">English</option>
                  <option value="yoruba">Yoruba</option>
                  <option value="french">French</option>
                </select>
              </p>
            </div>

            {/* Secondary Input */}
            <div>
              <p className="text-text-secondary text-xl leading-relaxed">
                I'd like to{' '}
                <select
                  value={formData.tone}
                  onChange={(e) => handleInputChange('tone', e.target.value)}
                  className="inline-block mx-1 px-3 py-1 border-b-2 border-primary bg-transparent text-primary font-semibold focus:outline-none hover:border-primary/80 transition-colors rounded-none"
                >
                  <option value="academic">use academic tone</option>
                  <option value="casual">use casual tone</option>
                  <option value="professional">use professional tone</option>
                  <option value="creative">use creative tone</option>
                </select>
                {' '}to be used as my baseline
              </p>
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-8">
            <div className="text-center">
              <h2 className="text-3xl font-bold text-text-primary mb-4">Content Details</h2>
              <p className="text-text-secondary text-lg">Tell us about your content to generate the perfect document</p>
            </div>

            <div className="space-y-8">
              <div>
                <label className="block text-base font-medium text-text-primary mb-4">
                  What's your main topic? *
                </label>
                <Input
                  type="text"
                  placeholder="e.g., Digital Marketing, Biology, Business Strategy"
                  value={formData.niche}
                  onChange={(e) => handleInputChange('niche', e.target.value)}
                  className="w-full h-14 text-lg rounded-lg border-2 focus:border-primary focus:ring-0 px-4"
                />
              </div>

              <div>
                <label className="block text-base font-medium text-text-primary mb-4">
                  Who is your target audience? *
                </label>
                <Input
                  type="text"
                  placeholder="e.g., Beginners, University students, Business professionals"
                  value={formData.targetAudience}
                  onChange={(e) => handleInputChange('targetAudience', e.target.value)}
                  className="w-full h-14 text-lg rounded-lg border-2 focus:border-primary focus:ring-0 px-4"
                />
              </div>

              <div>
                <label className="block text-base font-medium text-text-primary mb-4">
                  Key topics to cover (optional)
                </label>
                <Input
                  type="text"
                  placeholder="e.g., SEO, content marketing, social media"
                  value={formData.keywords}
                  onChange={(e) => handleInputChange('keywords', e.target.value)}
                  className="w-full h-14 text-lg rounded-lg border-2 focus:border-primary focus:ring-0 px-4"
                />
              </div>
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-8">
            <div className="text-center">
              <h2 className="text-3xl font-bold text-text-primary mb-4">Choose template</h2>
              <p className="text-text-secondary text-lg">Select a design template for your document</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {[
                { id: 'modern', name: 'Modern', description: 'Clean and professional design', preview: '📄' },
                { id: 'academic', name: 'Academic', description: 'Traditional academic format', preview: '📚' },
                { id: 'business', name: 'Business', description: 'Corporate presentation style', preview: '💼' },
                { id: 'creative', name: 'Creative', description: 'Artistic and engaging layout', preview: '🎨' }
              ].map((template) => (
                <button
                  key={template.id}
                  onClick={() => handleInputChange('template', template.id)}
                  className={`p-8 rounded-lg border-2 transition-all hover:scale-105 ${
                    formData.template === template.id
                      ? 'border-primary bg-primary/5 shadow-lg scale-105'
                      : 'border-border hover:border-primary/50 hover:shadow-md'
                  }`}
                >
                  <div className="text-center">
                    <div className="text-5xl mb-4">{template.preview}</div>
                    <h3 className="font-semibold text-text-primary text-lg">{template.name}</h3>
                    <p className="text-text-secondary mt-2">{template.description}</p>
                  </div>
                </button>
              ))}
            </div>
          </div>
        );

      case 4:
        return (
          <div className="space-y-8">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-text-primary mb-3">Review</h2>
              <p className="text-text-secondary">Review your document settings before generation</p>
            </div>

            <div className="space-y-6">
              <div className="p-6 bg-primary/5 rounded-lg border border-primary/20">
                <h3 className="font-semibold text-text-primary mb-4">Document Summary</h3>
                <div className="space-y-3 text-sm">
                  <div className="flex justify-between">
                    <span className="text-text-secondary">Type:</span>
                    <span className="font-medium text-text-primary">{documentTypes.find(t => t.id === formData.documentType)?.name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-text-secondary">Topic:</span>
                    <span className="font-medium text-text-primary">{formData.niche || 'Not specified'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-text-secondary">Audience:</span>
                    <span className="font-medium text-text-primary">{formData.targetAudience || 'Not specified'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-text-secondary">Language:</span>
                    <span className="font-medium text-text-primary">{languageOptions.find(l => l.id === formData.language)?.name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-text-secondary">Tone:</span>
                    <span className="font-medium text-text-primary">{toneOptions.find(t => t.id === formData.tone)?.name}</span>
                  </div>
                </div>
              </div>

              <div className="p-4 bg-accent/10 rounded-lg border border-accent/20 text-center">
                <Icon name="Zap" size={24} className="mx-auto mb-2 text-accent" />
                <p className="text-sm text-text-secondary">
                  Ready to generate your document using AI
                </p>
              </div>
            </div>
          </div>
        );

      case 5:
        return (
          <div className="space-y-8">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-text-primary mb-3">Publish</h2>
              <p className="text-text-secondary">Your document has been generated successfully!</p>
            </div>

            <div className="text-center space-y-6">
              <div className="w-20 h-20 bg-success/10 rounded-full flex items-center justify-center mx-auto">
                <Icon name="CheckCircle" size={40} className="text-success" />
              </div>

              <div>
                <h3 className="text-lg font-semibold text-text-primary mb-2">Document Ready</h3>
                <p className="text-text-secondary">
                  Your {formData.documentType} about "{formData.niche}" is ready for download and sharing.
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button variant="primary" iconName="Download" className="px-6">
                  Download PDF
                </Button>
                <Button variant="outline" iconName="Share" className="px-6">
                  Share Document
                </Button>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* Step Progress Indicator - Designrr Style */}
      <div className="px-8 py-6 bg-surface border-b border-border">
        <StepProgressIndicator
          steps={steps}
          currentStep={currentStep}
          onStepClick={(stepId) => stepId <= currentStep && onStepChange(stepId)}
        />
      </div>

      {/* Main Content Area - Centered Layout */}
      <div className="flex-1 flex items-center justify-center p-8 bg-background">
        <div className="w-full max-w-2xl">
          {/* Step Content */}
          <div className="bg-surface rounded-lg border border-border p-8 shadow-card">
            {renderStepContent()}
          </div>

          {/* Navigation Buttons - Centered */}
          <div className="flex justify-center space-x-6 mt-12">
            {currentStep > 1 && (
              <Button
                variant="outline"
                onClick={handlePrevious}
                iconName="ChevronLeft"
                iconPosition="left"
                className="px-8 py-3 text-base rounded-lg border-2"
              >
                Previous
              </Button>
            )}

            {currentStep < steps.length ? (
              <Button
                variant="primary"
                onClick={handleNext}
                iconName="ChevronRight"
                iconPosition="right"
                className="px-12 py-3 text-base rounded-lg shadow-lg hover:shadow-xl transition-all"
              >
                {currentStep === 1 ? "Let's start" : "Next"}
              </Button>
            ) : (
              <Button
                variant="success"
                onClick={() => console.log('Generate document with:', formData)}
                iconName="Sparkles"
                iconPosition="left"
                className="px-12 py-3 text-base rounded-lg shadow-lg hover:shadow-xl transition-all"
              >
                Publish
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreationWizard;